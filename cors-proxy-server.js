#!/usr/bin/env node

/**
 * Simple CORS Proxy Server for TempStick API
 * Bypasses browser CORS restrictions for development testing
 */

import express from 'express'
import cors from 'cors'
import fetch from 'node-fetch'
import dotenv from 'dotenv'
import { createClient } from '@supabase/supabase-js'

// Load environment variables from .env.local first, then .env
dotenv.config({ path: '.env.local' })
dotenv.config()

const app = express()
const PORT = 3002

// Initialize Supabase client with service role for data ingestion
const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.VITE_SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY
)

// Require a valid system user id for attribution (FK auth.users.id)
const SYNC_USER_ID = process.env.TEMPSTICK_SYNC_USER_ID
const UUID_REGEX = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i

// Track sync status
let lastSyncTime = null
let syncInProgress = false

// Enable CORS for all routes
app.use(cors({
  origin: ['http://localhost:5177', 'http://localhost:5178', 'http://localhost:3000'],
  methods: ['GET', 'POST', 'PUT', 'DELETE'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-API-KEY', 'x-api-version']
}))

app.use(express.json())

const TEMPSTICK_API_BASE = 'https://tempstickapi.com/api/v1'

// Enhanced health check endpoint
app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    lastSyncTime,
    syncInProgress,
    apiKeyConfigured: !!process.env.VITE_TEMPSTICK_API_KEY,
    supabaseConfigured: !!process.env.VITE_SUPABASE_URL,
    syncUserConfigured: !!SYNC_USER_ID
  })
})

// Proxy middleware - fixed route pattern
app.use('/api/v1', async (req, res) => {
  try {
    const apiKey = process.env.VITE_TEMPSTICK_API_KEY
    
    if (!apiKey) {
      return res.status(400).json({
        error: 'TempStick API key not configured',
        message: 'Set VITE_TEMPSTICK_API_KEY in your .env file'
      })
    }

    // Build the target URL
    const targetPath = req.path.replace('/api/v1', '')
    const queryString = req.url.includes('?') ? req.url.split('?')[1] : ''
    const targetUrl = `${TEMPSTICK_API_BASE}${targetPath}${queryString ? '?' + queryString : ''}`
    
    console.log(`🌡️  Proxying: ${req.method} ${targetUrl}`)
    
    // Forward the request to TempStick API
    const response = await fetch(targetUrl, {
      method: req.method,
      headers: {
        'X-API-KEY': apiKey,
        'User-Agent': '', // Empty user agent as recommended by TempStick docs
        'Accept-Encoding': 'gzip, deflate' // Accept gzipped responses
      },
      body: req.method !== 'GET' ? JSON.stringify(req.body) : undefined
    })

    const data = await response.text()
    
    // Set response headers
    res.status(response.status)
    res.set('Content-Type', response.headers.get('content-type') || 'application/json')
    
    if (response.ok) {
      console.log(`✅ Success: ${response.status} ${response.statusText}`)
      try {
        const jsonData = JSON.parse(data)
        res.json(jsonData)
      } catch (e) {
        res.send(data)
      }
    } else {
      console.log(`❌ Error: ${response.status} ${response.statusText}`)
      console.log(`Response: ${data}`)
      
      res.json({
        error: 'TempStick API Error',
        status: response.status,
        message: data,
        url: targetUrl
      })
    }

  } catch (error) {
    console.error('Proxy error:', error)
    res.status(500).json({
      error: 'Proxy Server Error',
      message: error.message
    })
  }
})

// Sync endpoint for historical data collection
app.post('/sync', async (req, res) => {
  if (!SYNC_USER_ID || !UUID_REGEX.test(SYNC_USER_ID)) {
    return res.status(400).json({
      success: false,
      error: 'TEMPSTICK_SYNC_USER_ID is missing or not a valid UUID',
      message: 'Set TEMPSTICK_SYNC_USER_ID in your .env to a valid Supabase auth user id.'
    })
  }
  if (syncInProgress) {
    return res.status(429).json({
      error: 'Sync already in progress',
      lastSyncTime
    })
  }

  try {
    syncInProgress = true
    console.log('🔄 Starting historical data sync...')
    
    const startTime = Date.now()
    const limit = req.query.limit ? parseInt(req.query.limit) : 5
    
    // Get all sensors first
    const sensorsResponse = await fetch(`${TEMPSTICK_API_BASE}/sensors/all`, {
      headers: {
        'X-API-KEY': process.env.VITE_TEMPSTICK_API_KEY,
        'User-Agent': ''
      }
    })
    
    if (!sensorsResponse.ok) {
      throw new Error(`Failed to fetch sensors: ${sensorsResponse.statusText}`)
    }
    
    const sensorsData = await sensorsResponse.json()
    let totalRecords = 0
    const errors = []
    
    // Process each sensor
    for (const sensor of sensorsData?.data?.items || []) {
      try {
        const extId = sensor.sensor_id || sensor.id
        const sensorName = sensor.sensor_name || sensor.name || 'TempStick Sensor'
        if (!extId) {
          console.warn('⚠️ Skipping sensor with no external id:', sensor)
          continue
        }
        console.log(`📊 Syncing sensor: ${sensorName} (${extId})`)
        
        // Upsert sensor info and fetch internal UUID
        const { data: upserted, error: sensorError } = await supabase
          .from('sensors')
          .upsert({
            user_id: SYNC_USER_ID,
            sensor_id: extId,
            name: sensorName,
            device_name: sensorName,
            is_active: true,
            updated_at: new Date().toISOString()
          }, {
            onConflict: 'user_id,sensor_id'
          })
          .select('id')
          .single()

        if (sensorError || !upserted) {
          console.error(`❌ Sensor upsert error for ${extId}:`, sensorError)
          errors.push({ sensor: extId, error: 'sensor_upsert', details: sensorError })
          continue
        }
        const internalSensorId = upserted.id
        
        // Use current sensor data from the API response instead of fetching separate readings
        if (sensor.last_temp !== undefined && sensor.last_checkin) {
          const temperature = parseFloat(sensor.last_temp)
          const humidity = sensor.last_humidity ? parseFloat(sensor.last_humidity) : null
          const recordedAt = new Date(sensor.last_checkin + ' UTC').toISOString()
          
          const readingRow = {
            user_id: SYNC_USER_ID,
            sensor_id: internalSensorId,
            recorded_at: recordedAt,
            temp_celsius: temperature,
            temp_fahrenheit: (Number.isFinite(temperature) ? (temperature * 9/5) + 32 : null),
            humidity: humidity,
            battery_level: sensor.battery_pct ? parseInt(sensor.battery_pct) : null,
            signal_strength: sensor.rssi ? parseInt(sensor.rssi) : null,
            within_safe_range: true,
            temp_violation: false,
            data_source: 'tempstick_api'
          }

          const { error: upsertErr, data } = await supabase
            .from('temperature_readings')
            .insert([readingRow])
            .select('id')
          if (upsertErr) {
            console.error('❌ Reading insert error:', upsertErr)
            errors.push({ sensor: extId, error: 'reading_insert', details: upsertErr })
          } else {
            totalRecords += (data && data.length) || 1
            console.log(`📊 Added reading for ${sensorName}: ${temperature}°C`)
          }
        } else {
          console.log(`⚠️ No temperature data for sensor ${sensorName}`)
        }
        
      } catch (sensorError) {
        console.error(`❌ Error processing sensor ${sensor.sensor_id || sensor.id}:`, sensorError)
        errors.push({ sensor: sensor.sensor_id || sensor.id, error: 'processing', details: sensorError.message })
      }
    }
    
    const duration = Date.now() - startTime
    lastSyncTime = new Date().toISOString()
    
    console.log(`✅ Sync completed: ${totalRecords} records in ${duration}ms`)
    
    res.json({
      success: true,
      timestamp: lastSyncTime,
      duration,
      recordsProcessed: totalRecords,
      sensorsProcessed: sensorsData?.data?.items?.length || 0,
      errors: errors.length > 0 ? errors : null
    })
    
  } catch (error) {
    console.error('❌ Sync failed:', error)
    res.status(500).json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    })
  } finally {
    syncInProgress = false
  }
})

// Start the server
app.listen(PORT, () => {
  console.log('🚀 CORS Proxy Server Started')
  console.log('================================')
  console.log(`📍 Listening on: http://localhost:${PORT}`)
  console.log(`🔑 API Key: ${process.env.VITE_TEMPSTICK_API_KEY ? '✅ Configured' : '❌ Missing'}`)
  console.log(`🗄️  Supabase: ${process.env.VITE_SUPABASE_URL ? '✅ Configured' : '❌ Missing'}`)
  console.log(`👤 Sync User: ${SYNC_USER_ID ? '✅ Configured' : '❌ Missing (set TEMPSTICK_SYNC_USER_ID)'}`)
  console.log(`🌡️  Proxying to: ${TEMPSTICK_API_BASE}`)
  console.log('================================')
  console.log('')
  console.log('Usage:')
  console.log(`GET  http://localhost:${PORT}/api/v1/sensors`)
  console.log(`GET  http://localhost:${PORT}/api/v1/readings/recent`)
  console.log(`POST http://localhost:${PORT}/sync?limit=5`)
  console.log(`GET  http://localhost:${PORT}/health`)
  console.log('')
  console.log('Historical Data Collection:')
  console.log('- Run POST /sync to collect and store historical data')
  console.log('- Data is stored in Supabase for dashboard queries')
  console.log('- Set TEMPSTICK_SYNC_USER_ID in .env for multi-user setups')
})
