import { supabase } from '../lib/supabase';
import { VoiceEvent, VoiceEventFilters, VoiceEventData } from '../types/schema';
import type { PostgrestFilterBuilder } from '@supabase/postgrest-js';
import { voiceEventCache } from './VoiceEventCache';
import type { CacheStats } from './VoiceEventCache';
import { voicePerformanceMonitor } from './VoicePerformanceMonitor';
import type { DashboardData } from './VoicePerformanceMonitor';

/**
 * Optimized Voice Event Service with caching and performance monitoring
 * Implements pagination, intelligent caching, and real-time optimizations
 */

interface PaginatedResult<T> {
  data: T[];
  total: number;
  page: number;
  pageSize: number;
  hasMore: boolean;
}

interface QueryOptimizationOptions {
  useCache?: boolean;
  cachePageOnly?: boolean;
  prefetchNext?: boolean;
  monitor?: boolean;
}

export class OptimizedVoiceEventService {
  private static instance: OptimizedVoiceEventService;
  private subscriptions = new Map<string, { unsubscribe: () => void }>();

  constructor() {
    // Singleton pattern
    if (OptimizedVoiceEventService.instance) {
      return OptimizedVoiceEventService.instance;
    }
    OptimizedVoiceEventService.instance = this;
  }

  /**
   * Get paginated voice events with caching and performance monitoring
   */
  async getVoiceEventsPaginated(
    filters: VoiceEventFilters = {},
    page: number = 1,
    pageSize: number = 20,
    options: QueryOptimizationOptions = {}
  ): Promise<PaginatedResult<VoiceEvent>> {
    const {
      useCache = true,
      cachePageOnly = false,
      prefetchNext = false,
      monitor = true,
    } = options;

    const operationId = `get_voice_events_${Date.now()}`;

    if (monitor) {
      voicePerformanceMonitor.startTimer(operationId);
    }

    try {
      // Check cache first
      const cacheKey = this.generateCacheKey(filters, page, pageSize);

      if (useCache) {
        const cached = voiceEventCache.getCachedVoiceEvents({ ...filters, page, pageSize });
        if (cached) {
          voicePerformanceMonitor.monitorCacheOperation('hit', 'voice_events', cacheKey);

          const total = (await this.getCachedTotal(filters)) ?? (await this.getTotal(filters));

          if (monitor) {
            voicePerformanceMonitor.endTimer(operationId, 'voice.query.latency', {
              page: page.toString(),
              pageSize: pageSize.toString(),
              cached: 'true',
            });
          }

          return {
            data: cached,
            total,
            page,
            pageSize,
            hasMore: page * pageSize < total,
          };
        }
        voicePerformanceMonitor.monitorCacheOperation('miss', 'voice_events', cacheKey);
      }

      // Build optimized query
      const result = await this.executeOptimizedQuery(filters, page, pageSize);

      // Cache results
      if (useCache) {
        if (cachePageOnly) {
          voiceEventCache.cacheVoiceEvents({ ...filters, page, pageSize }, result.data);
        } else {
          voiceEventCache.cacheVoiceEvents(filters, result.data);
        }

        // Cache total count separately with longer TTL
        this.cacheTotal(filters, result.total);
      }

      // Prefetch next page
      if (prefetchNext && result.hasMore) {
        this.prefetchNextPage(filters, page + 1, pageSize);
      }

      if (monitor) {
        voicePerformanceMonitor.endTimer(operationId, 'voice.query.latency', {
          page: page.toString(),
          pageSize: pageSize.toString(),
          cached: 'false',
        });
      }

      return result;
    } catch (error) {
      if (monitor) {
        voicePerformanceMonitor.endTimer(operationId, 'voice.query.latency', {
          success: 'false',
          error: error instanceof Error ? error.message : 'unknown',
        });
      }
      throw error;
    }
  }

  /**
   * Execute optimized database query
   */
  private async executeOptimizedQuery(
    filters: VoiceEventFilters,
    page: number,
    pageSize: number
  ): Promise<PaginatedResult<VoiceEvent>> {
    return voicePerformanceMonitor.monitorDatabaseQuery(
      'get_voice_events_paginated',
      async () => {
        const offset = (page - 1) * pageSize;

        // Build the query with optimized column selection
        let query = supabase
          .from('inventory_events')
          .select(
            `
            id,
            event_type,
            name,
            quantity,
            unit,
            notes,
            occurred_at,
            created_at,
            voice_confidence_score,
            voice_confidence_breakdown,
            raw_transcript,
            audio_recording_url,
            created_by_voice,
            metadata
          `,
            { count: 'exact' }
          )
          .eq('created_by_voice', true)
          .order('created_at', { ascending: false })
          .range(offset, offset + pageSize - 1);

        // Apply filters with optimized conditions
        query = this.applyOptimizedFilters(query, filters);

        const { data, error, count } = await query;

        if (error) {
          throw new Error(`Failed to fetch voice events: ${error.message}`);
        }

        return {
          data: (data ?? []) as VoiceEvent[],
          total: count ?? 0,
          page,
          pageSize,
          hasMore: count ? page * pageSize < count : false,
        };
      },
      {
        operation: 'paginated_query',
        page: page.toString(),
        filters: JSON.stringify(filters),
      }
    );
  }

  /**
   * Apply optimized filters to query
   */
  private applyOptimizedFilters(
    query: PostgrestFilterBuilder<Record<string, unknown>, VoiceEvent, VoiceEvent[], unknown>,
    filters: VoiceEventFilters
  ): PostgrestFilterBuilder<Record<string, unknown>, VoiceEvent, VoiceEvent[], unknown> {
    // Date range filter (most selective, apply first)
    if (filters.dateRange) {
      query = query
        .gte('created_at', filters.dateRange.start)
        .lte('created_at', filters.dateRange.end);
    }

    // Event type filter (indexed)
    if (filters.eventType && filters.eventType.length > 0) {
      query = query.in('event_type', filters.eventType);
    }

    // Confidence threshold filter
    if (filters.confidenceThreshold !== undefined) {
      query = query.gte('voice_confidence_score', filters.confidenceThreshold);
    }

    // Search query (applied last as it's least selective)
    if (filters.searchQuery) {
      const searchTerm = `%${filters.searchQuery.toLowerCase()}%`;
      query = query.or(`raw_transcript.ilike.${searchTerm},notes.ilike.${searchTerm}`);
    }

    return query;
  }

  /**
   * Get total count with caching
   */
  private async getTotal(filters: VoiceEventFilters): Promise<number> {
    return voicePerformanceMonitor.monitorDatabaseQuery(
      'get_voice_events_count',
      async () => {
        let countQuery = supabase
          .from('inventory_events')
          .select('id', { count: 'exact', head: true })
          .eq('created_by_voice', true);

        countQuery = this.applyOptimizedFilters(countQuery, filters);

        const { count, error } = await countQuery;

        if (error) {
          throw new Error(`Failed to get event count: ${error.message}`);
        }

        return count ?? 0;
      },
      { operation: 'count_query' }
    );
  }

  /**
   * Cache total count
   */
  private cacheTotal(filters: VoiceEventFilters, total: number): void {
    const cacheKey = `total_${JSON.stringify(filters)}`;
    voiceEventCache.set(cacheKey, total, 2 * 60 * 1000); // 2 minutes TTL
  }

  /**
   * Get cached total count
   */
  private getCachedTotal(filters: VoiceEventFilters): number | null {
    const cacheKey = `total_${JSON.stringify(filters)}`;
    return voiceEventCache.get<number>(cacheKey);
  }

  /**
   * Prefetch next page in background
   */
  private async prefetchNextPage(
    filters: VoiceEventFilters,
    nextPage: number,
    pageSize: number
  ): Promise<void> {
    // Don't await this - run in background
    setTimeout(async () => {
      try {
        await this.getVoiceEventsPaginated(filters, nextPage, pageSize, {
          useCache: true,
          monitor: false,
        });
      } catch (error) {
        console.warn('Prefetch failed:', error);
      }
    }, 100);
  }

  /**
   * Generate cache key
   */
  private generateCacheKey(filters: VoiceEventFilters, page: number, pageSize: number): string {
    return JSON.stringify({ filters, page, pageSize });
  }

  /**
   * Create voice event with performance monitoring
   */
  async createVoiceEvent(
    eventData: VoiceEventData & {
      voice_confidence_score: number;
      voice_confidence_breakdown: Record<string, unknown>;
      raw_transcript: string;
      audio_recording_url?: string;
    }
  ): Promise<VoiceEvent> {
    const operationId = `create_voice_event_${Date.now()}`;
    voicePerformanceMonitor.startTimer(operationId);

    try {
      const result = await voicePerformanceMonitor.monitorDatabaseQuery(
        'create_voice_event',
        async () => {
          const insertData = {
            event_type: eventData.event_type,
            quantity: eventData.quantity,
            unit: eventData.unit,
            notes: eventData.notes ?? '',
            occurred_at: eventData.occurred_at ?? new Date().toISOString(),
            voice_confidence_score: eventData.voice_confidence_score,
            voice_confidence_breakdown: eventData.voice_confidence_breakdown,
            raw_transcript: eventData.raw_transcript,
            audio_recording_url: eventData.audio_recording_url,
            created_by_voice: true,
            metadata: {
              product_name: eventData.product_name,
              vendor_name: eventData.vendor_name,
              customer_name: eventData.customer_name,
              condition: eventData.condition,
              temperature: eventData.temperature,
              temperature_unit: eventData.temperature_unit,
              processing_method: eventData.processing_method,
              quality_grade: eventData.quality_grade,
              market_form: eventData.market_form,
              voice_processed: true,
              processing_timestamp: new Date().toISOString(),
            },
          };

          const { data, error } = await supabase
            .from('inventory_events')
            .insert(insertData)
            .select()
            .single();

          if (error) {
            throw new Error(`Failed to create voice event: ${error.message}`);
          }

          return data as VoiceEvent;
        },
        { operation: 'insert' }
      );

      // Invalidate related caches
      voiceEventCache.invalidateVoiceEventCaches();

      voicePerformanceMonitor.endTimer(operationId, 'voice.create.latency', {
        success: 'true',
      });

      return result;
    } catch (error) {
      voicePerformanceMonitor.endTimer(operationId, 'voice.create.latency', {
        success: 'false',
        error: error instanceof Error ? error.message : 'unknown',
      });
      throw error;
    }
  }

  /**
   * Search voice events with intelligent caching
   */
  async searchVoiceEvents(
    query: string,
    limit: number = 20,
    options: QueryOptimizationOptions = {}
  ): Promise<VoiceEvent[]> {
    const { useCache = true, monitor = true } = options;

    if (monitor) {
      voicePerformanceMonitor.startTimer(`search_${Date.now()}`);
    }

    // Check cache first
    if (useCache) {
      const cached = voiceEventCache.getCachedVoiceEvents({
        searchQuery: query,
        limit,
      } as VoiceEventFilters & { limit: number });

      if (cached) {
        voicePerformanceMonitor.monitorCacheOperation('hit', 'voice_search', query);
        return cached;
      }
      voicePerformanceMonitor.monitorCacheOperation('miss', 'voice_search', query);
    }

    const result = await voicePerformanceMonitor.monitorDatabaseQuery(
      'search_voice_events',
      async () => {
        const searchTerm = `%${query.toLowerCase()}%`;

        const { data, error } = await supabase
          .from('inventory_events')
          .select(
            `
            id,
            event_type,
            quantity,
            unit,
            notes,
            occurred_at,
            created_at,
            voice_confidence_score,
            raw_transcript,
            metadata
          `
          )
          .eq('created_by_voice', true)
          .or(
            `raw_transcript.ilike.${searchTerm},notes.ilike.${searchTerm},metadata->>product_name.ilike.${searchTerm}`
          )
          .order('created_at', { ascending: false })
          .limit(limit);

        if (error) {
          throw new Error(`Search failed: ${error.message}`);
        }

        return (data ?? []) as VoiceEvent[];
      },
      { query: query.slice(0, 50) } // Truncate long queries for logging
    );

    // Cache results
    if (useCache) {
      voiceEventCache.cacheVoiceEvents(
        {
          searchQuery: query,
          limit,
        } as VoiceEventFilters & { limit: number },
        result,
        5 * 60 * 1000
      ); // 5 minutes for search results
    }

    return result;
  }

  /**
   * Setup real-time subscription with optimization
   */
  setupRealtimeSubscription(
    filters: VoiceEventFilters,
    callback: (event: VoiceEvent) => void,
    subscriptionId: string = 'default'
  ): () => void {
    // Remove existing subscription
    this.removeRealtimeSubscription(subscriptionId);

    const subscription = supabase
      .channel(`voice_events_${subscriptionId}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'inventory_events',
          filter: 'created_by_voice=eq.true',
        },
        (payload) => {
          const newEvent = payload.new as VoiceEvent;

          // Apply filters
          if (this.eventMatchesFilters(newEvent, filters)) {
            // Invalidate relevant caches
            voiceEventCache.invalidateVoiceEventCaches();

            // Monitor real-time events
            voicePerformanceMonitor.recordMetric('realtime.event.received', 1, {
              event_type: newEvent.event_type,
              subscription: subscriptionId,
            });

            callback(newEvent);
          }
        }
      )
      .subscribe((status) => {
        console.log(`Real-time subscription ${subscriptionId} status:`, status);
        voicePerformanceMonitor.recordMetric('realtime.subscription.status', 1, {
          status,
          subscription: subscriptionId,
        });
      });

    this.subscriptions.set(subscriptionId, subscription);

    // Return cleanup function
    return () => this.removeRealtimeSubscription(subscriptionId);
  }

  /**
   * Check if event matches filters
   */
  private eventMatchesFilters(event: VoiceEvent, filters: VoiceEventFilters): boolean {
    if (filters.eventType && filters.eventType.length > 0) {
      if (!filters.eventType.includes(event.event_type)) {
        return false;
      }
    }

    if (filters.confidenceThreshold !== undefined) {
      if ((event.voice_confidence_score ?? 0) < filters.confidenceThreshold) {
        return false;
      }
    }

    if (filters.dateRange) {
      const eventDate = new Date(event.created_at ?? event.occurred_at ?? '').getTime();
      const startDate = new Date(filters.dateRange.start).getTime();
      const endDate = new Date(filters.dateRange.end).getTime();

      if (eventDate < startDate || eventDate > endDate) {
        return false;
      }
    }

    return true;
  }

  /**
   * Remove real-time subscription
   */
  removeRealtimeSubscription(subscriptionId: string): void {
    const subscription = this.subscriptions.get(subscriptionId);
    if (subscription) {
      subscription.unsubscribe();
      this.subscriptions.delete(subscriptionId);

      voicePerformanceMonitor.recordMetric('realtime.subscription.removed', 1, {
        subscription: subscriptionId,
      });
    }
  }

  /**
   * Get performance statistics
   */
  getPerformanceStats(): { cache: CacheStats; performance: DashboardData; subscriptions: number } {
    return {
      cache: voiceEventCache.getStats(),
      performance: voicePerformanceMonitor.getDashboardData(),
      subscriptions: this.subscriptions.size,
    };
  }

  /**
   * Cleanup resources
   */
  cleanup(): void {
    // Remove all subscriptions
    for (const subscriptionId of this.subscriptions.keys()) {
      this.removeRealtimeSubscription(subscriptionId);
    }

    // Clear caches
    voiceEventCache.clear();
  }
}

// Export singleton instance
export const optimizedVoiceEventService = new OptimizedVoiceEventService();
