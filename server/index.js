// Minimal Express server for voice realtime and tool endpoints
// Run in development: `node server/index.js` (PORT defaults to 3001)

import express from 'express';
import cors from 'cors';
import fetch from 'node-fetch';
import OpenAI from 'openai';
import { WebSocketServer, WebSocket } from 'ws';
import { createServer } from 'http';
import dotenv from 'dotenv';

// Load environment variables from .env.local first, then .env (for local dev)
dotenv.config({ path: '.env.local' });
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3001;

app.use(cors({ origin: true }));
app.use(express.json());

// Debug endpoint to check environment variables
app.get('/api/debug/env', (req, res) => {
  res.json({
    REALTIME_MODEL: process.env.REALTIME_MODEL,
    VITE_REALTIME_MODEL: process.env.VITE_REALTIME_MODEL,
    REALTIME_VOICE: process.env.REALTIME_VOICE,
    hasOpenAIKey: !!process.env.OPENAI_API_KEY,
    hasViteOpenAIKey: !!process.env.VITE_OPENAI_API_KEY,
    nodeEnv: process.env.NODE_ENV,
    openaiKeyLength: process.env.OPENAI_API_KEY ? process.env.OPENAI_API_KEY.length : 0,
    viteOpenaiKeyLength: process.env.VITE_OPENAI_API_KEY ? process.env.VITE_OPENAI_API_KEY.length : 0
  });
});

// Proxy for OpenAI Realtime WebRTC SDP negotiation to avoid browser CORS
// Accept raw SDP text and forward to OpenAI with correct headers
app.post('/api/openai/realtime/calls', express.text({ type: 'application/sdp' }), async (req, res) => {
  try {
    const auth = req.headers['authorization'];
    if (!auth) {
      return res.status(401).send('Missing Authorization header');
    }

    const model = (req.query?.model
      || process.env.REALTIME_MODEL
      || process.env.VITE_REALTIME_MODEL
      || 'gpt-realtime');
    const upstreamUrl = `https://api.openai.com/v1/realtime/calls${model ? `?model=${encodeURIComponent(String(model))}` : ''}`;

    const upstream = await fetch(upstreamUrl, {
      method: 'POST',
      headers: {
        'Authorization': auth,
        'Accept': 'application/sdp',
        'Content-Type': 'application/sdp',
        ...(process.env.OPENAI_ORG ? { 'OpenAI-Organization': process.env.OPENAI_ORG } : {}),
      },
      body: typeof req.body === 'string' ? req.body : '',
    });

    const contentType = upstream.headers.get('content-type') || '';
    res.status(upstream.status);
    if (contentType) res.set('Content-Type', contentType);

    // Return raw text (SDP or error text)
    const text = await upstream.text();
    return res.send(text);
  } catch (err) {
    console.error('Realtime calls proxy error:', err);
    return res.status(502).send('Realtime calls proxy error');
  }
});

function resolveOpenAIClient() {
  const apiKey = process.env.OPENAI_API_KEY || process.env.OPENAI_API_KEY_PRIMARY;
  if (!apiKey) {
    return null;
  }
  return new OpenAI({ apiKey });
}

// Health/capability check for realtime
app.all('/api/voice-realtime-check', async (req, res) => {
  try {
    console.log('🔍 Environment check:');
    console.log('  OPENAI_API_KEY:', process.env.OPENAI_API_KEY ? 'SET' : 'NOT SET');
    console.log('  OPENAI_API_KEY_PRIMARY:', process.env.OPENAI_API_KEY_PRIMARY ? 'SET' : 'NOT SET');
    console.log('  VITE_OPENAI_API_KEY:', process.env.VITE_OPENAI_API_KEY ? 'SET' : 'NOT SET');
    const hasServerKey = Boolean(process.env.OPENAI_API_KEY || process.env.OPENAI_API_KEY_PRIMARY);
    const enableDirect = String(process.env.ENABLE_DIRECT_REALTIME || process.env.VITE_ENABLE_DIRECT_REALTIME) === 'true';
    const supported = hasServerKey || !enableDirect;
    return res.status(200).json({ supported, mode: enableDirect ? 'direct' : 'fallback', hasServerKey });
  } catch (err) {
    console.error('voice-realtime-check error:', err);
    return res.status(200).json({ supported: false });
  }
});

// Environment diagnostics (dev convenience)
app.get('/api/env-check', (req, res) => {
  try {
    const mask = (s) => (typeof s === 'string' && s.length > 8 ? `${s.slice(0, 4)}…${s.slice(-4)}` : undefined);
    const out = {
      nodeEnv: process.env.NODE_ENV || 'development',
      openai: {
        hasServerKey: Boolean(process.env.OPENAI_API_KEY || process.env.OPENAI_API_KEY_PRIMARY),
        hasViteClientKey: Boolean(process.env.VITE_OPENAI_API_KEY),
      },
      supabase: {
        hasUrl: Boolean(process.env.VITE_SUPABASE_URL),
        hasAnonKey: Boolean(process.env.VITE_SUPABASE_ANON_KEY),
      },
      tempstick: {
        hasKey: Boolean(process.env.VITE_TEMPSTICK_API_KEY || process.env.TEMPSTICK_API_KEY),
        keyMasked: mask(process.env.VITE_TEMPSTICK_API_KEY || process.env.TEMPSTICK_API_KEY || ''),
      },
    };
    res.status(200).json(out);
  } catch (err) {
    res.status(500).json({ error: 'env_check_failed' });
  }
});

// TempStick API relay (dev convenience): forwards /api/v1/* to tempstickapi.com/api/v1/*
// In Express 5, prefer a mounted middleware instead of wildcard patterns
app.use('/api/v1', async (req, res) => {
  try {
    const original = req.originalUrl || req.url || '';
    const tail = original.replace(/^\/api\/v1\/?/, '');
    const [pathOnly, qs] = tail.split('?');
    const apiPath = (pathOnly || '').replace(/^\//, '');
    const url = `https://tempstickapi.com/api/v1/${apiPath}${qs ? `?${qs}` : ''}`;
    const apiKey = process.env.VITE_TEMPSTICK_API_KEY || process.env.TEMPSTICK_API_KEY;
    
    if (!apiKey) {
      return res.status(500).json({ error: 'Missing TempStick API key (set VITE_TEMPSTICK_API_KEY in .env)' });
    }
    
    // Prepare headers - don't send Content-Type for GET requests (TempStick doesn't like it)
    const headers = {
      'X-API-KEY': apiKey,
      // TempStick API prefers empty UA to avoid firewall blocks
      'User-Agent': '',
      'Accept': 'application/json',
      'Accept-Encoding': 'gzip, deflate',
    };
    
    // Only add Content-Type for non-GET requests
    if (!['GET', 'HEAD'].includes(req.method)) {
      headers['Content-Type'] = req.headers['content-type'] || 'application/json';
    }
    
    const upstream = await fetch(url, {
      method: req.method,
      headers,
      body: ['GET','HEAD'].includes(req.method) ? undefined : JSON.stringify(req.body ?? {}),
    });
    
    // Handle GZIP compressed responses
    const contentType = upstream.headers.get('content-type') || 'application/json';
    res.status(upstream.status);
    res.set('Content-Type', contentType);
    
    // node-fetch automatically handles gzip decompression when we call .json() or .text()
    if (contentType.includes('application/json')) {
      try {
        const json = await upstream.json();
        return res.json(json);
      } catch (parseError) {
        console.error('TempStick JSON parse error:', parseError);
        const text = await upstream.text();
        return res.send(text);
      }
    } else {
      const text = await upstream.text();
      return res.send(text);
    }
  } catch (err) {
    console.error('TempStick proxy error:', err);
    return res.status(502).json({ error: 'TempStick proxy error' });
  }
});

// Ephemeral token minting for browser WebRTC auth
app.all('/api/voice/ephemeral-token', async (req, res) => {
  if (req.method !== 'POST' && req.method !== 'GET') {
    res.set('Allow', 'GET, POST');
    return res.status(405).json({ error: 'Method not allowed' });
  }
  const apiKey = process.env.OPENAI_API_KEY || process.env.OPENAI_API_KEY_PRIMARY;
  if (!apiKey) {
    return res.status(500).json({ error: 'OPENAI_API_KEY not configured' });
  }
  const model = (req.query.model || process.env.REALTIME_MODEL || 'gpt-realtime');
  const voice = (req.query.voice || process.env.REALTIME_VOICE || 'alloy');
  try {
    const r = await fetch('https://api.openai.com/v1/realtime/sessions', {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
        ...(process.env.OPENAI_ORG ? { 'OpenAI-Organization': process.env.OPENAI_ORG } : {}),
      },
      // Note: 'expires_in' is not supported by the sessions API — omit it
      body: JSON.stringify({ model, voice }),
    });
    const data = await r.json().catch(() => ({}));
    if (!r.ok || !data?.client_secret) {
      console.error('Failed to mint ephemeral token:', data);
      return res.status(r.status || 500).json({ error: data?.error?.message || data?.error || 'Failed to mint token' });
    }
    return res.status(200).json(data);
  } catch (err) {
    console.error('Ephemeral token error:', err);
    return res.status(502).json({ error: 'Network error' });
  }
});

app.post('/api/voice/realtime-session', async (req, res) => {
  try {
    const apiKey = process.env.OPENAI_API_KEY || process.env.OPENAI_API_KEY_PRIMARY;
    if (!apiKey) {
      return res.status(500).json({ error: 'OPENAI_API_KEY not configured' });
    }

    const {
      model = process.env.REALTIME_MODEL || 'gpt-4o-realtime-preview-2024-12-17',
      voice = process.env.REALTIME_VOICE || 'alloy',
      instructions,
      modalities,
    } = req.body ?? {};

    const body = {
      model,
      voice,
      modalities: Array.isArray(modalities) && modalities.length > 0 ? modalities : ['audio', 'text'],
      instructions:
        instructions ||
        'You are the Pacific Cloud Seafoods realtime assistant. Help manage seafood inventory with short, precise voice responses.',
    };

    const upstream = await fetch('https://api.openai.com/v1/realtime/sessions', {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
        ...(process.env.OPENAI_ORG ? { 'OpenAI-Organization': process.env.OPENAI_ORG } : {}),
      },
      body: JSON.stringify(body),
    });

    const data = await upstream.json().catch(() => ({}));

    if (!upstream.ok) {
      console.error('Realtime session creation failed:', data);
      return res.status(upstream.status).json({ error: data?.error?.message || data?.error || 'Failed to create realtime session' });
    }

    if (process.env.NODE_ENV !== 'production') {
      console.log('Realtime session created:', {
        id: data?.id,
        model: data?.model,
      });
    }

    return res.status(200).json(data);
  } catch (err) {
    console.error('Realtime session creation failed:', err);
    return res.status(500).json({ error: 'Failed to create realtime session' });
  }
});

// Optional: serve static built app if present (production use)
// Uncomment after running `npm run build`
// import path from 'path';
// import { fileURLToPath } from 'url';
// const __filename = fileURLToPath(import.meta.url);
// const __dirname = path.dirname(__filename);
// const distPath = path.join(__dirname, '..', 'dist');
// app.use(express.static(distPath));
// app.get('*', (_req, res) => {
//   res.sendFile(path.join(distPath, 'index.html'));
// });

// Create HTTP server to support both Express and WebSocket
const server = createServer(app);

// WebSocket server for OpenAI Realtime API relay
const wss = new WebSocketServer({ 
  server,
  path: '/api/realtime-relay'
});

// Store active connections
const connections = new Map();

function generateSessionId() {
  return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

wss.on('connection', async (clientWs, request) => {
  console.log('🔗 Client connected to realtime relay');
  
  const sessionId = generateSessionId();
  // Prefer server-side OPENAI_API_KEY; fall back to VITE_ for convenience
  const apiKey = process.env.OPENAI_API_KEY || process.env.VITE_OPENAI_API_KEY;
  
  if (!apiKey) {
    console.error('❌ OpenAI API key not configured');
    clientWs.send(JSON.stringify({
      type: 'error',
      error: { message: 'OpenAI API key not configured', code: 'config_error' }
    }));
    clientWs.close();
    return;
  }

  try {
    // Connect to OpenAI Realtime API
    const wsModel = process.env.REALTIME_MODEL || 'gpt-realtime';
    const openaiWs = new WebSocket(
      `wss://api.openai.com/v1/realtime?model=${encodeURIComponent(wsModel)}`,
      {
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          ...(process.env.OPENAI_ORG ? { 'OpenAI-Organization': process.env.OPENAI_ORG } : {}),
        }
      }
    );

    // Store connection pair
    connections.set(sessionId, { clientWs, openaiWs, sessionId });

    // Handle OpenAI WebSocket events
    openaiWs.on('open', () => {
      console.log(`✅ OpenAI connection established for session ${sessionId}`);
    });

    openaiWs.on('message', (data, isBinary) => {
      // Forward message to client unchanged (preserve binary/text)
      if (clientWs.readyState === WebSocket.OPEN) {
        try {
          clientWs.send(data, { binary: isBinary });
        } catch (err) {
          console.error('Error forwarding message to client:', err);
        }
      }
    });

    openaiWs.on('error', (error) => {
      console.error('OpenAI WebSocket error:', error);
      if (clientWs.readyState === WebSocket.OPEN) {
        clientWs.send(JSON.stringify({
          type: 'error',
          error: { message: 'OpenAI connection error', code: 'connection_error' }
        }));
      }
    });

    openaiWs.on('close', (code, reason) => {
      console.log(`OpenAI connection closed: ${code} ${reason}`);
      connections.delete(sessionId);
      if (clientWs.readyState === WebSocket.OPEN) {
        clientWs.close(code, reason.toString());
      }
    });

    // Handle client WebSocket events
    clientWs.on('message', (data, isBinary) => {
      // Forward message to OpenAI unchanged (preserve binary/text)
      if (openaiWs.readyState === WebSocket.OPEN) {
        try {
          openaiWs.send(data, { binary: isBinary });
        } catch (err) {
          console.error('Error forwarding message to OpenAI:', err);
        }
      }
    });

    // Keepalive ping to prevent idle disconnects
    const pingInterval = setInterval(() => {
      if (clientWs.readyState === WebSocket.OPEN) {
        try { clientWs.ping(); } catch {}
      }
      if (openaiWs.readyState === WebSocket.OPEN) {
        try { openaiWs.ping(); } catch {}
      }
    }, 20000);

    clientWs.on('error', (error) => {
      console.error('Client WebSocket error:', error);
    });

    clientWs.on('close', (code, reason) => {
      console.log(`Client connection closed: ${code} ${reason}`);
      connections.delete(sessionId);
      if (openaiWs.readyState === WebSocket.OPEN) {
        openaiWs.close();
      }
      clearInterval(pingInterval);
    });

  } catch (error) {
    console.error('Error setting up relay connection:', error);
    if (clientWs.readyState === WebSocket.OPEN) {
      clientWs.send(JSON.stringify({
        type: 'error',
        error: { message: 'Failed to establish relay connection', code: 'relay_error' }
      }));
      clientWs.close();
    }
  }
});

server.listen(PORT, () => {
  console.log(`🚀 Server listening on http://localhost:${PORT}`);
  console.log(`📡 WebSocket relay available at ws://localhost:${PORT}/api/realtime-relay`);
});
