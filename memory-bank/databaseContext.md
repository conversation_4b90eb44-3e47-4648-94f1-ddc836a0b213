# Database Context

## Supabase Instance

- Instance: puzjricwpsjusj<PERSON><PERSON><PERSON>wen (remote only)
- Tables:
  - products
  - events
  - inventory_events (unified table)
  - categories
  - product_categories
- Storage:
  - Image storage bucket
  - File type validation
  - Size limits
  - Access control

## Events Table

- UUID primary keys
- Event types:
  - receiving
  - sales
- Product references
- Flexible metadata storage
- Timestamp tracking
- Transaction support
- Audit logging

## Units Handling

- Units managed at event level
- Supports multiple units per product:
  - lbs (default)
  - kg
  - cases
  - units
- Context-specific to inventory transactions
- Allows different units for different events of same product
- Unit conversion support
- Validation rules

## Data Storage

1. **Product Catalog**
   - Name
   - Category
   - Subcategory
   - Default unit
   - Price history
   - Supplier info
   - Origin data
   - Storage requirements

2. **Inventory Events**
   - Event type
   - Product reference
   - Quantity and unit
   - Price/cost information
   - Transaction metadata
   - Source tracking
   - Audit information

3. **Image Storage**
   - Product images
   - Document scans
   - File validation
   - Size optimization
   - Access control
   - Backup support

## Integration Points

1. **Voice Input**
   - Direct database insertion
   - Transaction support
   - Validation checks
   - Error handling
   - Retry mechanism
   - Audit logging

2. **File Import**
   - Batch processing
   - Data transformation
   - Schema validation
   - Error collection
   - Progress tracking
   - Transaction management

3. **Data Export**
   - CSV generation
   - Report formatting
   - Data filtering
   - Custom queries
   - Aggregation support

## Data Consistency

1. **Validation Rules**
   - Required fields
   - Data types
   - Value ranges
   - Unit compatibility
   - Cross-references
   - Business logic

2. **Transaction Management**
   - Atomic operations
   - Rollback support
   - Error recovery
   - Consistency checks
   - Audit trails

3. **Error Handling**
   - Validation errors
   - Network issues
   - Timeout handling
   - Retry logic
   - User feedback

## Categories Tables

- **categories**
  - Purpose: general app categories (e.g., Receiving, Disposal, Physical Count, Re-processing)
  - Accessed by UI and checks in `src/lib/setupDatabase.ts`
  - Seeded by Node-only script `scripts/seed-categories.mjs` using a Supabase service role key

- **product_categories**
  - Purpose: product taxonomy used by `"Products"` via FK `product_category_id`
  - Created/seeded via Supabase migrations under `supabase/migrations/*product_categories*.sql`
  - Queried by helper `getProductCategories()` in `src/lib/setupDatabase.ts`

## Authentication

- Supabase Auth integration
- Role-based access
- Token management
- Session handling
- Security rules

## Environment Setup

1. **Development Environment (LOCAL)**
   - Local Supabase Docker instance at http://127.0.0.1:54321
   - All development work uses local database
   - Docker containers managed via Supabase CLI
   - Local Studio UI available at http://127.0.0.1:54323

2. **Production Environment (REMOTE)**
   - Production instance at puzjricwpsjusjlgrwen.supabase.co
   - Used only for production deployments
   - Managed service handles backups and maintenance

3. **Configuration**
   - Development: Uses .env.local with local Supabase URLs
   - Production: Uses .env.production with remote URLs
   - Environment variables:
     - VITE_SUPABASE_URL (local: http://127.0.0.1:54321, prod: https://puzjricwpsjusjlgrwen.supabase.co)
     - VITE_SUPABASE_ANON_KEY (different for local vs prod)
     - VITE_SUPABASE_SERVICE_ROLE_KEY (for server-side operations)

4. **Development Workflow**
   - Local development against Docker Supabase instance
   - Migration management through Supabase CLI
   - Testing in local environment
   - Schema changes via migration files
   - Push to production only when ready
